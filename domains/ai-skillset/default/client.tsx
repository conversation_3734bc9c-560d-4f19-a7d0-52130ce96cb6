'use client';

import type { ToolInvocation } from '@ai-sdk/ui-utils';
import React from 'react';
import type { AIIntentUIResolveDTO } from '@bika/types/ai/dto';
import { AIIntentUIVOSchemas } from '@bika/types/ai/vo';
import { AIWizardIntentUIArea } from '../../ai/client/wizard/ai-wizard-intent-ui-area';
import { SkillsetUIMap } from '../types';
import { ImageSkillsetName } from './types';

const toolsetUI: SkillsetUIMap = {
  /**
   *
   * @deprecated 老的组件，不推荐使用，改用 ai-consulting-quick skillsets
   */
  // 'ai-consulting-deprecated': {
  //   artifact: (props: ArtifactUIProps) => {
  //     if (props.toolInvocation.args?.consultingType === 'engineer') {
  //       return <NodeResourcesArtifact resources={props.toolInvocation.args?.resources} />;
  //     }
  //     if (props.toolInvocation.args?.consultingType === 'marketer') {
  //       return <HtmlArtifact content={props.toolInvocation.args?.content} />;
  //     }
  //     if (['business-analyst', 'consultant'].includes(props.toolInvocation.args?.consultingType)) {
  //       return <MarkdownArtifact content={props.toolInvocation.args?.content} />;
  //     }
  //     return <>{props.toolInvocation.args?.content}</>;
  //   },
  //   component: undefined,
  // },
  form: {
    component: (props: {
      toolInvocation: ToolInvocation;
      onClick?: () => void;
      sendMessage?: (msg: string) => Promise<void>;
      sendUI?: (uiResolve: AIIntentUIResolveDTO) => Promise<void>;
    }) => {
      const args = props.toolInvocation.args;
      const intentUIVO = AIIntentUIVOSchemas.parse(args);
      return (
        <>
          <AIWizardIntentUIArea
            disabled={false}
            intentUI={intentUIVO}
            loading={false}
            resolutionStatus={''}
            type={''}
            sendUI={props.sendUI!}
            sendMessage={props.sendMessage!}
            close={async () => {}}
          />
        </>
      );
    },
  },
  load_attachment: {
    artifact: 'server-artifact',
    component: undefined,
  },
  [ImageSkillsetName.image_to_text]: {
    artifact: 'server-artifact',
    component: () => ({
      // 使用合适的图标，转换为 INodeIconValue 格式
      // icon: {
      //   kind: 'enum' as const,
      //   icon: 'components/service_outlined',
      // },
    }),
  },
  read_node_content: {
    artifact: 'server-artifact',
  },
};

export default toolsetUI;
